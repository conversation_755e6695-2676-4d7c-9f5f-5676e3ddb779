import { createHash } from "crypto"
import { IVectorStore, PointStruct, VectorStoreSearchResult, Payload } from "./interfaces/vector-store"

/**
 * Local in-memory vector store for code index
 */
export class LocalVectorStore implements IVectorStore {
	private readonly vectors: Map<string, number[]> = new Map()
	private readonly payloads: Map<string, Payload> = new Map()
	private readonly vectorSize: number

	constructor(vectorSize: number) {
		this.vectorSize = vectorSize
	}

	async initialize(): Promise<boolean> {
		this.vectors.clear()
		this.payloads.clear()
		return true
	}

	async upsertPoints(points: PointStruct[]): Promise<void> {
		for (const point of points) {
			if (point.vector.length !== this.vectorSize) {
				throw new Error(`Vector size mismatch: expected ${this.vectorSize}, got ${point.vector.length}`)
			}
			if (isPayload(point.payload)) {
				this.vectors.set(point.id, point.vector)
				this.payloads.set(point.id, point.payload)
			}
		}
	}

// 类型守卫函数，移到类外部
function isPayload(obj: any): obj is Payload {
	return obj && typeof obj.filePath === "string" && typeof obj.codeChunk === "string" && typeof obj.startLine === "number" && typeof obj.endLine === "number"
}

	async search(queryVector: number[], directoryPrefix?: string, minScore = 0.4, maxResults = 50): Promise<VectorStoreSearchResult[]> {
		if (queryVector.length !== this.vectorSize) {
			throw new Error(`Query vector size mismatch: expected ${this.vectorSize}, got ${queryVector.length}`)
		}
		const results: VectorStoreSearchResult[] = []
		for (const [id, vector] of this.vectors.entries()) {
			if (directoryPrefix && !id.startsWith(directoryPrefix)) continue
			const score = cosineSimilarity(queryVector, vector)
			if (score >= minScore) {
				results.push({ id, score, payload: this.payloads.get(id) })
			}
		}
		results.sort((a, b) => b.score - a.score)
		return results.slice(0, maxResults)
	}

	async deletePointsByFilePath(filePath: string): Promise<void> {
		for (const id of this.vectors.keys()) {
			const payload = this.payloads.get(id)
			if (payload && payload.filePath === filePath) {
				this.vectors.delete(id)
				this.payloads.delete(id)
			}
		}
	}

	async deletePointsByMultipleFilePaths(filePaths: string[]): Promise<void> {
		const filePathSet = new Set(filePaths)
		for (const id of this.vectors.keys()) {
			const payload = this.payloads.get(id)
			if (payload && filePathSet.has(payload.filePath)) {
				this.vectors.delete(id)
				this.payloads.delete(id)
			}
		}
	}

	async clearCollection(): Promise<void> {
		this.vectors.clear()
		this.payloads.clear()
	}

	async deleteCollection(): Promise<void> {
		this.vectors.clear()
		this.payloads.clear()
	}

	async collectionExists(): Promise<boolean> {
		return this.vectors.size > 0
	}
}

function cosineSimilarity(a: number[], b: number[]): number {
	let dot = 0, normA = 0, normB = 0
	for (let i = 0; i < a.length; i++) {
		dot += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}
	return normA && normB ? dot / (Math.sqrt(normA) * Math.sqrt(normB)) : 0
}

/**
 * Local index manager for codebase
 */
// ...existing code...

// ...existing code...
// ...existing code...
}
